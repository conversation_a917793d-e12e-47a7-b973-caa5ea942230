<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1940233c-31e9-4708-95d4-04d503c1e051" name="Changes" comment="getsnackbar">
      <change beforePath="$PROJECT_DIR$/petdash/frontend/lib/features/screen/business/Screen/MyProducts/editNewProducts.dart" beforeDir="false" afterPath="$PROJECT_DIR$/petdash/frontend/lib/features/screen/business/Screen/MyProducts/editNewProducts.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/petdash/frontend/lib/features/screen/shop/Service/ServicesList.dart" beforeDir="false" afterPath="$PROJECT_DIR$/petdash/frontend/lib/features/screen/shop/Service/ServicesList.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/petdash/frontend/lib/provider/business_provider.dart" beforeDir="false" afterPath="$PROJECT_DIR$/petdash/frontend/lib/provider/business_provider.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/petdash/frontend/lib/services/BusinessServices/business_service.dart" beforeDir="false" afterPath="$PROJECT_DIR$/petdash/frontend/lib/services/BusinessServices/business_service.dart" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Dart File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/petdash" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;history&quot;: [
    {
      &quot;state&quot;: &quot;OPEN&quot;,
      &quot;reviewState&quot;: &quot;AWAITING_REVIEW&quot;,
      &quot;author&quot;: &quot;Divy2003&quot;
    }
  ],
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;reviewState&quot;: &quot;AWAITING_REVIEW&quot;,
    &quot;author&quot;: &quot;Divy2003&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Divy2003/petdash.git&quot;,
    &quot;accountId&quot;: &quot;b4ff287d-3937-4e75-86a3-ac928625cd36&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30a73dZZlk9FHr9WndLbfkqwtfb" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Flutter.petdash.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;io.flutter.reload.alreadyRun&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/flutter project/petdash/petdashnew&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;dart.settings&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\flutter project\petdash\petdashnew\petdash\frontend\lib\services\BusinessServices" />
      <recent name="D:\flutter project\petdash\petdashnew\petdash\frontend\lib\services\petowerServices" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="petdash" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/petdash/frontend/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1940233c-31e9-4708-95d4-04d503c1e051" name="Changes" comment="" />
      <created>1753853632003</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753853632003</updated>
    </task>
    <task id="LOCAL-00001" summary="businessprofileapi">
      <option name="closed" value="true" />
      <created>1753869799037</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753869799037</updated>
    </task>
    <task id="LOCAL-00002" summary="servicelist">
      <option name="closed" value="true" />
      <created>1753941906880</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753941906880</updated>
    </task>
    <task id="LOCAL-00003" summary="Myproductsapi">
      <option name="closed" value="true" />
      <created>1754043631301</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754043631302</updated>
    </task>
    <task id="LOCAL-00004" summary="Myproductsapi">
      <option name="closed" value="true" />
      <created>1754044319335</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754044319335</updated>
    </task>
    <task id="LOCAL-00005" summary="Myproductsapi">
      <option name="closed" value="true" />
      <created>1754044353376</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754044353376</updated>
    </task>
    <task id="LOCAL-00006" summary="getsnackbar">
      <option name="closed" value="true" />
      <created>1754046912749</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754046912749</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="businessprofileapi" />
    <MESSAGE value="servicelist" />
    <MESSAGE value="Myproductsapi" />
    <MESSAGE value="getsnackbar" />
    <option name="LAST_COMMIT_MESSAGE" value="getsnackbar" />
  </component>
</project>